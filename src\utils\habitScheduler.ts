import cron from 'node-cron';
import Habit from '../models/Habit';

// Function to add daily entries to all habits
export const addDailyHabitEntries = async (): Promise<void> => {
  try {
    console.log('Running daily habit entries update...');
    
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const habits = await Habit.find({});
    let updatedCount = 0;
    
    for (const habit of habits) {
      const todayExists = habit.todoList.some(item => {
        const itemDate = new Date(item.date);
        itemDate.setHours(0, 0, 0, 0);
        return itemDate.getTime() === today.getTime();
      });
      
      if (!todayExists) {
        habit.todoList.push({
          date: today,
          isCompleted: false
        });
        await habit.save();
        updatedCount++;
      }
    }
    
    console.log(`Daily habit entries updated for ${updatedCount} habits`);
  } catch (error) {
    console.error('Error adding daily habit entries:', error);
  }
};

// Schedule the daily task to run at midnight every day
export const startHabitScheduler = (): void => {
  // Run at 00:01 every day (1 minute after midnight)
  cron.schedule('1 0 * * *', addDailyHabitEntries, {
    timezone: 'UTC'
  });
  
  console.log('Habit scheduler started - will run daily at 00:01 UTC');
};

// Manual trigger for testing
export const triggerDailyUpdate = addDailyHabitEntries;
