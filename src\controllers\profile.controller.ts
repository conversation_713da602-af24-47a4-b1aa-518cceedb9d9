import { Response } from 'express';
import User from '../models/User';
import bcrypt from 'bcrypt';
import { StatusCodes } from 'http-status-codes';
import { AuthenticatedRequest } from '../middlewares/auth';

export const createProfile = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { username, gender, dob, about } = req.body;
    const userId = req.user?.userId;

    if (!userId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        message: 'User not authenticated'
      });
      return;
    }

    console.log("User ID in createProfile:", userId);

    // Find the user based on the authenticated userId
    const user = await User.findById(userId);
    if (!user) {
      res.status(StatusCodes.NOT_FOUND).json({ message: 'User not found' });
      return;
    }

    // Check if profile already exists (user already has profile fields filled)
    if (user.username) {
      res.status(StatusCodes.BAD_REQUEST).json({ message: 'Profile already exists' });
      return;
    }

    if(!username || !gender || !dob || !about) {
      res.status(StatusCodes.BAD_REQUEST).json({ message: 'All fields are required' });
      return;
    }

    // Update user with profile fields
    user.username = username;
    user.gender = gender;
    user.dob = new Date(dob);
    user.about = about;

    await user.save();

    const userResponse = {
      _id: user._id,
      email: user.email,
      username: user.username,
      gender: user.gender,
      dob: user.dob,
      about: user.about
    };

    res.status(StatusCodes.CREATED).json({
      message: 'Profile created successfully',
      profile: userResponse
    });
  } catch (error) {
    console.error('Error creating profile:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      message: 'Error creating profile',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

export const updateProfile = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { username, gender, dob, about } = req.body;
    const userId = req.user?.userId;

    if (!userId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        message: 'User not authenticated'
      });
      return;
    }

    // Find the user based on the authenticated userId
    const user = await User.findById(userId);

    if (!user) {
      res.status(StatusCodes.NOT_FOUND).json({ message: 'User not found' });
      return;
    }

    // Update the profile fields if provided
    if (username !== undefined) {
      user.username = username;
    }
    if (gender !== undefined) {
      user.gender = gender;
    }
    if (dob !== undefined) {
      user.dob = new Date(dob);
    }
    if (about !== undefined) {
      user.about = about;
    }

    // Save the updated user
    await user.save();

    // Return user without password
    const userResponse = {
      _id: user._id,
      email: user.email,
      username: user.username,
      gender: user.gender,
      dob: user.dob,
      about: user.about
    };

    res.status(StatusCodes.OK).json({
      message: 'Profile updated successfully',
      profile: userResponse
    });
  } catch (error: unknown) {
    if (error instanceof Error) {
      console.error('Error updating profile:', error);
      res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        message: 'Error updating profile',
        error: error.message,
      });
      return;
    }
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      message: 'Unknown error occurred while updating profile',
    });
  }
};

export const getProfile = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const userId = req.user?.userId;

    if (!userId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        message: 'User not authenticated'
      });
      return;
    }

    // Find the user based on the authenticated userId
    const user = await User.findById(userId);

    if (!user) {
      res.status(StatusCodes.NOT_FOUND).json({ message: 'User not found' });
      return;
    }

    // Return user profile without password
    const userProfile = {
      _id: user._id,
      email: user.email,
      username: user.username,
      gender: user.gender,
      dob: user.dob,
      about: user.about
    };

    res.status(StatusCodes.OK).json({
      message: 'Profile retrieved successfully',
      profile: userProfile
    });
  } catch (error: unknown) {
    if (error instanceof Error) {
      console.error('Error retrieving profile:', error);
      res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        message: 'Error retrieving profile',
        error: error.message,
      });
      return;
    }
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      message: 'Unknown error occurred while retrieving profile',
    });
  }
};

export const changePassword = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { oldPassword, newPassword, confirmPassword } = req.body;
    const userId = req.user?.userId;

    if (!userId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        message: 'User not authenticated'
      });
      return;
    }

    // Validate required fields
    if (!oldPassword || !newPassword || !confirmPassword) {
      res.status(StatusCodes.BAD_REQUEST).json({
        message: 'All password fields are required'
      });
      return;
    }

    // Find the user based on the authenticated userId
    const user = await User.findById(userId);
    if (!user) {
      res.status(StatusCodes.NOT_FOUND).json({ message: 'User not found' });
      return;
    }

    // Check if the old password matches
    const isMatch = await bcrypt.compare(oldPassword, user.password);
    if (!isMatch) {
      res.status(StatusCodes.BAD_REQUEST).json({ message: 'Old password is incorrect' });
      return;
    }

    // Ensure new password is not the same as old password
    if (oldPassword === newPassword) {
      res.status(StatusCodes.BAD_REQUEST).json({
        message: 'New password cannot be the same as the old password'
      });
      return;
    }

    // Ensure new password and confirm password match
    if (newPassword !== confirmPassword) {
      res.status(StatusCodes.BAD_REQUEST).json({
        message: 'New password and confirm password do not match'
      });
      return;
    }

    // Hash the new password
    const hashedPassword = await bcrypt.hash(newPassword, 10);

    // Update the user's password in the database
    user.password = hashedPassword;
    await user.save();

    // Send success response
    res.status(StatusCodes.OK).json({ message: 'Password changed successfully' });
  } catch (error: unknown) {
    // Type assertion to handle the error as an instance of Error
    if (error instanceof Error) {
      console.error('Error changing password:', error);
      res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        message: 'Error changing password',
        error: error.message,
      });
      return;
    }

    // In case the error is not an instance of Error (shouldn't happen in most cases)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      message: 'Unknown error occurred while changing password',
    });
  }
};