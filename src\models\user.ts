import mongoose, { Schema } from 'mongoose';
import { IUser } from '../interfaces/index';

const userSchema: Schema = new Schema({
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  otp: { type: String },
  otpCreatedAt: { type: Date },
  username: { type: String },
  gender: { type: String },
  dob: { type: Date },
  about: { type: String },
  habitCompletionPercentage: {
    type: Number,
    default: 0
  }
}, {
  timestamps: true
});

const User = mongoose.model<IUser>('User', userSchema);
export default User;
