{"name": "flowcus", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"build": "npx tsc", "start": "node dist/server.js", "dev": "nodemon src/server.ts"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.775.0", "@types/bcrypt": "^5.0.2", "@types/multer": "^1.4.12", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.17", "@types/qrcode": "^1.5.5", "bcrypt": "^5.1.1", "body-parser": "^1.20.3", "cors": "^2.8.5", "dotenv": "^16.6.1", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "google-translate-api-x": "^10.7.2", "helmet": "^8.1.0", "http-status-codes": "^2.3.0", "i": "^0.3.7", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "langdetect": "^0.2.1", "mongodb": "^6.15.0", "mongoose": "^8.13.0", "multer": "^1.4.5-lts.2", "node-cron": "^4.2.1", "nodemailer": "^6.10.1", "nodemon": "^3.1.9", "npm": "^11.3.0", "qrcode": "^1.5.4"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^5.0.1", "@types/jsonwebtoken": "^9.0.9", "@types/langdetect": "^0.2.2", "ts-node-dev": "^2.0.0", "typescript": "^5.8.2"}}