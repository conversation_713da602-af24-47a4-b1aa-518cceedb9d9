import type { Request, Response } from 'express';
import dotenv from 'dotenv';
import bcrypt from 'bcrypt';
import User from '../models/User';
import jwt from 'jsonwebtoken';
import { StatusCodes } from 'http-status-codes';

dotenv.config();

const JWT_SECRET = process.env.JWT_SECRET;
if (!JWT_SECRET) {
  throw new Error('JWT_SECRET is not defined in the environment variables');
}

export const signup = async (req: Request, res: Response) => {
  const { email, password, confirmPassword } = req.body;

  try {
    if (password !== confirmPassword) {
    console.log(password, confirmPassword);
    return res.status(StatusCodes.BAD_REQUEST).json({ message: 'Passwords do not match' });
  }
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(StatusCodes.BAD_REQUEST).json({ message: 'User already exists' });
    }
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create the new user
    const newUser = new User({
      email,
      password: hashedPassword
    });

    await newUser.save();

    // Create JWT token
    const payload = { userId: newUser._id, email: newUser.email }
    const accessToken = jwt.sign(payload, JWT_SECRET, 
      { expiresIn: '30d' }); 
    return res.status(StatusCodes.OK).json({
      message: "Signed Up Successfully!",
      user: newUser,
      accessToken,
    });
  } catch (error) {
    console.error(error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({ message: 'Error signing up', error }); // 
  }
};



// Login function
export const login = async (req: Request, res: Response) => {
  const { email, password } = req.body;

  try {
    // Check if user exists
    const user = await User.findOne({ email });
    if (!user) {
      return res.status(StatusCodes.UNAUTHORIZED).json({ message: 'Invalid email or account not registered' });
    }

    // Verify the password
    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      return res.status(StatusCodes.UNAUTHORIZED).json({ message: 'Invalid password' });
    }

    // Generate JWT token
    const payload = { userId: user._id, email: user.email };
    const accessToken = jwt.sign(payload, JWT_SECRET, { expiresIn: '30d' });

    return res.status(StatusCodes.OK).json({
      message: 'User Logged In Successfully',
      user: { email: user.email, userId: user._id }, 
      accessToken,
    });
  } catch (error) {
    console.error(error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({ message: 'Error logging in', error });
  }
};






// Reset password function after OTP verification
export const resetPassword = async (req: Request, res: Response) => {
  const { email, password, confirmPassword } = req.body;

  try {

    // Ensure new password is not the same as the old one
    const user = await User.findOne({ email });
    if (!user) {
      return res.status(StatusCodes.NOT_FOUND).json({ message: 'Email not found' });
    }

    const isSamePassword = await bcrypt.compare(password, user.password);
    if (isSamePassword) {
      return res.status(StatusCodes.BAD_REQUEST).json({ message: 'New password cannot be same as old, try a new password' });
    }

    // Ensure password and confirmPassword match
    if (password !== confirmPassword) {
      return res.status(StatusCodes.BAD_REQUEST).json({ message: 'Passwords do not match' });
    }
    const hashedPassword = await bcrypt.hash(password, 10);
    user.password = hashedPassword;
    await user.save();

    return res.status(StatusCodes.OK).json({
      message: 'Password reset successfully. Please return to the login page.',
    });
  } catch (error) {
    console.error(error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({ message: 'Error resetting password', error });
  }
};