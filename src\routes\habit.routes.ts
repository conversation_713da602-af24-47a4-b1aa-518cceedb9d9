import { Router } from 'express';
import {
  createHabit,
  getAllHabits,
  getAllHabitTitles,
  updateHabit,
  deleteHabit
} from '../controllers/habit.controller';
import { authenticateToken } from '../middlewares/index';
import { updateUserHabitCompletionPercentage } from '../controllers/helper/habits';

const router = Router();

router.use(authenticateToken);

router.post('/', createHabit);
router.get('/', getAllHabits);
router.get('/titles', getAllHabitTitles);
router.put('/:habitId', updateHabit);
router.delete('/:habitId', deleteHabit);

// when user mark the todays habit as true then if the percentage is not updating then call router.put('/:habitId', updateHabit); but for now its updating.
export default router;
