import mongoose, { Schema } from 'mongoose';
import { IHabit } from '../interfaces/habits.interface';

const todoListItemSchema: Schema = new Schema({
  date: { 
    type: Date, 
    required: true 
  },
  isCompleted: { 
    type: Boolean, 
    default: false 
  }
}, { _id: false });

const habitSchema: Schema = new Schema({
  userId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'User', 
    required: true 
  },
  title: { 
    type: String, 
    required: true,
    trim: true
  },
  icon: { 
    type: String, 
    required: true,
    trim: true
  },
  description: { 
    type: String, 
    required: true,
    trim: true
  },
  todoList: [todoListItemSchema]
}, {
  timestamps: true
});

// Index for better query performance
habitSchema.index({ userId: 1 });
habitSchema.index({ userId: 1, title: 1 });

// Pre-save middleware to automatically add today's date to todoList if not exists
habitSchema.pre('save', function(next) {
  const habit = this as any;
  const today = new Date();
  today.setHours(0, 0, 0, 0); // Set to start of day for consistent comparison
  
  // Check if today's date already exists in todoList
  const todayExists = habit.todoList.some((item: any) => {
    const itemDate = new Date(item.date);
    itemDate.setHours(0, 0, 0, 0);
    return itemDate.getTime() === today.getTime();
  });
  
  // If today's date doesn't exist, add it
  if (!todayExists) {
    habit.todoList.push({
      date: today,
      isCompleted: false
    });
  }
  
  next();
});

// Static method to add daily entries for all habits
habitSchema.statics.addDailyEntries = async function() {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  const habits = await this.find({});
  
  for (const habit of habits) {
    const todayExists = habit.todoList.some((item: any) => {
      const itemDate = new Date(item.date);
      itemDate.setHours(0, 0, 0, 0);
      return itemDate.getTime() === today.getTime();
    });
    
    if (!todayExists) {
      habit.todoList.push({
        date: today,
        isCompleted: false
      });
      await habit.save();
    }
  }
};

const Habit = mongoose.model<IHabit>('Habit', habitSchema);
export default Habit;
